import React from 'react';
import { handleGetAllUsersAction } from '@/actions/user.action';
import { handleGetAllSchoolsAction } from '@/actions/school.action';
import { ListingTemplate } from '@/components/templates/ListingTemplate/ListingTemplate';
import { ListingHeader } from '@/components/molecules/ListingHeader/ListingHeader';
import { ErrorDisplay } from '@/components/molecules/ErrorDisplay/ErrorDisplay';
import { UsersTable } from '@/components/organisms/UsersTable/UsersTable';
import { IUserResponse } from '@/apis/userApi';

export const dynamic = 'force-dynamic';

// This is a Server Component
const UsersManagementPage = async () => {

  // Fetch all users and schools
  let usersResponse;
  let schoolsResponse;
  let users: IUserResponse[] = [];
  let schools: { id: string; name: string }[] = [];
  let error = null;
  let isLoading = true;

  try {
    usersResponse = await handleGetAllUsersAction();
    schoolsResponse = await handleGetAllSchoolsAction();;

    if (usersResponse.status === 'success') {
      users = usersResponse.data || [];
      // Add status and lastActivity for demo purposes
      users = users.map(user => ({
        ...user,
        // Set status as 'active' if delete_at is null or undefined, otherwise use a random status
        status: !(user as any).delete_at ? 'active' : ['inactive', 'pending', 'suspended'][Math.floor(Math.random() * 3)],
        lastActivity: new Date(Date.now() - Math.floor(Math.random() * 30) * 24 * 60 * 60 * 1000).toISOString()
      }));
    } else {
      error = usersResponse.message || 'Failed to fetch users';
    }

    // Process schools data
    if (schoolsResponse.status === 'success') {
      const schoolsData = schoolsResponse.data || [];
      schools = schoolsData.map(school => ({ id: school.id, name: school.name }));
    } else {
    }
  } catch (err) {
    console.error('Exception while fetching data:', err);
    error = err instanceof Error ? err.message : 'An unexpected error occurred';
  } finally {
    isLoading = false;
  }

  const headerTable = (
    <ListingHeader 
      title="Users Management" 
      subtitle="Manage all users in the system"
      buttonProps={{
        label: "Create User",
        href: "/users-management/create",
        variant: "primary",
      }}
    />
  );

  const errorDisplay = <ErrorDisplay error={error as string} />;
  const table = <UsersTable users={users} error={error as string} isLoading={isLoading} schools={schools} />;

  return (
    <div className="flex flex-col w-full h-full"> 
      {error && errorDisplay}
      <ListingTemplate 
        header={headerTable}
        table={table}
      />
    </div>
  );
};

export default UsersManagementPage;
