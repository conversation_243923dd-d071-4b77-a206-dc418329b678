'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { handleGetAllSchoolsAction } from '@/actions/school.action';
import { EUserRole } from '@/config/enums/user';
import { ListingTemplate } from '@/components/templates/ListingTemplate/ListingTemplate';
import { ListingHeader } from '@/components/molecules/ListingHeader/ListingHeader';
import { UserForm } from '@/components/organisms/UserForm/UserForm';
import { SchoolModal } from '@/components/organisms/SchoolModal/SchoolModal';

const CreateUserPage = () => {
  const router = useRouter();
  const [showSchoolModal, setShowSchoolModal] = useState(false);
  const [schools, setSchools] = useState<{ id: string; name: string }[]>([]);
  const [isLoadingSchools, setIsLoadingSchools] = useState(false);
  const [formError, setFormError] = useState<string | null>(null);
  const [selectedSchoolId, setSelectedSchoolId] = useState<string | undefined>();

  // Fetch schools when the component mounts
  useEffect(() => {
    const fetchSchools = async () => {
      setIsLoadingSchools(true);
      try {
        const response = await handleGetAllSchoolsAction();
        if (response.status === 'success' && response.data) {
          setSchools(response.data.map(school => ({ id: school.id, name: school.name })));
        } else {
          if (response.status !== 'success') {
            console.error('Failed to fetch schools:', response.message);
          }
        }
      } catch (error) {
        console.error('Error fetching schools:', error);
      } finally {
        setIsLoadingSchools(false);
      }
    };

    fetchSchools();
  }, []);

  // Create the header component
  const header = (
    <ListingHeader 
      title="Create User" 
      subtitle="Add a new user to the system"
      buttonProps={{
        label: "Back to Users",
        href: "/users-management",
        variant: "secondary",
      }}
    />
  );

  // Handle school creation success
  const handleSchoolCreationSuccess = (data: { id: string; name: string }) => {
    setSchools(prev => [...prev, { id: data.id, name: data.name }]);
    setSelectedSchoolId(data.id);
  };

  return (
    <div className="container mx-auto p-4 max-w-6xl">
      <ListingTemplate 
        header={header}
        table={
          <UserForm 
            schools={schools}
            isLoadingSchools={isLoadingSchools}
            onCreateSchool={() => setShowSchoolModal(true)}
            selectedSchoolId={selectedSchoolId}
          />
        }
      />

      {/* Create School Modal */}
      <SchoolModal 
        isOpen={showSchoolModal}
        onClose={() => setShowSchoolModal(false)}
        onSuccess={handleSchoolCreationSuccess}
        onError={setFormError}
      />
    </div>
  );
};

export default CreateUserPage;
