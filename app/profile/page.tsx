'use client'; // Make this a client component

import React, { useState, useEffect } from 'react'; // Added
import { useSession } from 'next-auth/react'; // Changed from getServerSession for client component
import { useForm, SubmitHandler } from 'react-hook-form'; // Added
import { zodResolver } from '@hookform/resolvers/zod'; // Added
import * as z from 'zod'; // Added
import type { User } from 'next-auth';
// import { ProfileClientPage } from './profile-client-page'; // Removed
import { DetailTemplate } from '@/components/templates/DetailTemplate/DetailTemplate';
import { ListingHeader } from '@/components/molecules/ListingHeader/ListingHeader';
import DashboardTemplate from '@/components/templates/Dashboard/Dashboard'; // Kept for loading/error states initially
import { useSessionUpdate } from '@/hooks/useSessionUpdate'; // Added
import { Input } from '@/components/atoms/Input/Input'; // Added
import { Button } from '@/components/atoms/Button/Button'; // Added
import { Label } from '@/components/atoms/Label/Label'; // Added
import { Form } from '@/components/atoms/Form/Form'; // Added
import { updateUserProfileAction } from '@/actions/user.action'; // Added
import { IUpdateUserPayload, IUserResponse } from '@/apis/userApi'; // Added
import { AlertMessage } from '@/components/molecules/AlertMessage/AlertMessage'; // Added

// Define the Zod schema for profile updates (Moved from profile-client-page)
const profileSchema = z.object({
  name: z.string().min(1, 'Name is required').max(100, 'Name must be 100 characters or less'),
  email: z.string().email('Invalid email address'),
});

type ProfileFormValues = z.infer<typeof profileSchema>;

// export const metadata = { // metadata export is not allowed in client components
//   title: 'User Profile',
//   description: 'Manage your user profile.',
// };

export default function ProfilePage() { // Changed to non-async as it's a client component
  const { data: session, status } = useSession(); // Use client-side session
  const { updateSessionUser } = useSessionUpdate(); // Renamed from session to avoid conflict
  const [isLoading, setIsLoading] = useState(false);
  const [formError, setFormError] = useState<string | null>(null);
  const [formSuccess, setFormSuccess] = useState<string | null>(null);

  // initialUser will be derived from the session
  const initialUser = session?.user;
  const currentUser = initialUser; // For simplicity, can be refined if session structure differs

  const methods = useForm<ProfileFormValues>({
    resolver: zodResolver(profileSchema),
    // Default values will be set in useEffect once currentUser is available
  });

  const { register, handleSubmit, reset, formState: { errors, isDirty } } = methods;

  useEffect(() => {
    if (currentUser) {
      reset({
        name: currentUser.name || '',
        email: currentUser.email || '',
      });
    }
  }, [currentUser, reset]);

  const onSubmit: SubmitHandler<ProfileFormValues> = async (data) => {
    setIsLoading(true);
    setFormError(null);
    setFormSuccess(null);

    if (!currentUser?.id) {
      setFormError('User ID is missing. Cannot update profile.');
      setIsLoading(false);
      return;
    }

    const payload: IUpdateUserPayload = {
      name: data.name,
      email: data.email,
    };

    try {
      const result = await updateUserProfileAction(currentUser.id, payload);
      const updatedUserData = result.status === 'success' ? (result as any).updatedUser || result.data : null;

      if (result.status === 'success' && updatedUserData) {
        setFormSuccess(result.message || 'Profile updated successfully!');
        await updateSessionUser(updatedUserData); // Update session
        reset({ name: payload.name, email: payload.email });
      } else if (result.status === 'error') {
        const errorMessage = Array.isArray(result.message) ? result.message.join(', ') : result.message;
        setFormError(errorMessage || 'Failed to update profile.');
      }
    } catch (error) {
      setFormError('An unexpected error occurred. Please try again.');
      console.error('Profile update error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  if (status === 'loading') {
    return (
      <DashboardTemplate sidebarItems={[]} userMenuDropdownProps={{}} schoolInfo={null}>
        <div className="p-4 text-center">Loading profile...</div>
      </DashboardTemplate>
    );
  }

  if (!session?.user) {
    return (
      <DashboardTemplate sidebarItems={[]} userMenuDropdownProps={{}} schoolInfo={null}>
        <div className="p-4">
          <h1 className="text-2xl font-semibold">Access Denied</h1>
          <p>You must be logged in to view this page.</p>
        </div>
      </DashboardTemplate>
    );
  }
  
  // Ensure we have a user object, and it's not just a string (depends on session.user type)
  // This check is now implicitly handled by `session?.user` and `currentUser` derivation
  const user = session.user as User; // Cast to User type, assuming session.user is User compatible

  if (!user || !user.id) {
     return (
      <DashboardTemplate sidebarItems={[]} userMenuDropdownProps={{}} schoolInfo={null}>
        <div className="p-4">
          <h1 className="text-2xl font-semibold">Error</h1>
          <p>User data is not available or incomplete.</p>
        </div>
      </DashboardTemplate>
    );
  }


  const profileHeader = (
    <ListingHeader
      title="My Profile"
      subtitle="View and update your profile information"
    />
  );

  const profileContent = (
    <Form {...methods}>
      <div className="bg-white rounded-xl shadow-lg overflow-hidden">
        <div className="bg-gray-50 p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-800">Profile Information</h2>
          <p className="text-sm text-gray-600 mt-1">Update your personal details.</p>
        </div>
        <form onSubmit={handleSubmit(onSubmit)} className="p-6 space-y-6">
          {formError && <AlertMessage type="error" message={formError} />}
          {formSuccess && <AlertMessage type="success" message={formSuccess} />}
          <div>
            <Label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
              Full Name
            </Label>
            <Input
              id="name"
              type="text"
              {...register('name')}
              className={`pr-3 py-2 border ${errors.name ? 'border-red-300 focus:ring-red-500 focus:border-red-500' : 'border-gray-300 focus:ring-blue-500 focus:border-blue-500'} rounded-lg shadow-sm focus:outline-none focus:ring-2 transition-all duration-300 hover:border-blue-300 w-full`}
              placeholder="Enter your full name"
            />
            {errors.name && (
              <p className="mt-1 text-sm text-red-600 flex items-center gap-1">
                <svg className="h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                </svg>
                {errors.name.message}
              </p>
            )}
          </div>
          <div>
            <Label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
              Email Address
            </Label>
            <Input
              id="email"
              type="email"
              {...register('email')}
              className={`pr-3 py-2 border ${errors.email ? 'border-red-300 focus:ring-red-500 focus:border-red-500' : 'border-gray-300 focus:ring-blue-500 focus:border-blue-500'} rounded-lg shadow-sm focus:outline-none focus:ring-2 transition-all duration-300 hover:border-blue-300 w-full`}
              placeholder="Enter your email address"
            />
            {errors.email && (
              <p className="mt-1 text-sm text-red-600 flex items-center gap-1">
                <svg className="h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                </svg>
                {errors.email.message}
              </p>
            )}
          </div>
          {currentUser?.role && (
            <div>
              <Label htmlFor="role" className="block text-sm font-medium text-gray-700 mb-1">
                Role
              </Label>
              <p className="mt-1 w-full rounded-md border border-gray-200 bg-gray-50 px-3 py-2 text-sm text-gray-700 shadow-sm">
                {typeof currentUser.role === 'string' ? currentUser.role.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()) : ''}
              </p>
            </div>
          )}
          <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200">
            <Button
              type="button"
              variant="outline"
              onClick={() => reset({ name: currentUser?.name || '', email: currentUser?.email || ''})}
              disabled={isLoading || !isDirty}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              variant="primary"
              disabled={isLoading || !isDirty}
              isLoading={isLoading}
            >
              {isLoading ? 'Saving...' : 'Save Changes'}
            </Button>
          </div>
        </form>
      </div>
    </Form>
  );

  return (
    <div className="flex flex-col w-full h-full">
      <DetailTemplate
        header={profileHeader}
        content={profileContent}
      />
    </div>
  );
}
