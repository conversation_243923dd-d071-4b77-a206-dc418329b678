'use client';

import React from 'react';
import { useSession } from 'next-auth/react';
import { handleGetAllUsersAction } from '@/actions/user.action';
import { handleGetAllSchoolsAction } from '@/actions/school.action';
import { ListingTemplate } from '@/components/templates/ListingTemplate/ListingTemplate';
import { ListingHeader } from '@/components/molecules/ListingHeader/ListingHeader';
import { ErrorDisplay } from '@/components/molecules/ErrorDisplay/ErrorDisplay';
import { UsersTable } from '@/components/organisms/UsersTable/UsersTable';
import { IUserResponse } from '@/apis/userApi';
import { EUserRole } from '@/config/enums/user';

export const dynamic = 'force-dynamic';

// This is a Client Component for teacher management
const TeacherManagementPage = () => {
  const { data: session } = useSession();
  const [users, setUsers] = React.useState<IUserResponse[]>([]);
  const [schools, setSchools] = React.useState<{ id: string; name: string }[]>([]);
  const [error, setError] = React.useState<string | null>(null);
  const [isLoading, setIsLoading] = React.useState(true);

  // Fetch all users with TEACHER role and schools
  React.useEffect(() => {
    const fetchData = async () => {
      if (!session) {
        // Session is not yet available, or user is not logged in
        // You might want to handle this case, e.g., by showing a loading state or redirecting
        setIsLoading(false); // Stop loading if session is not available
        return;
      }

      try {
        setIsLoading(true);
        const schoolId = session.user?.schoolId; // Correctly get schoolId from session

        // Fetch teachers, filtering by schoolId if the user is a SCHOOL_MANAGER and always by role TEACHER
        console.log(`Fetching teachers for school: ${schoolId || 'all schools'}...`);
        const usersResponse = await handleGetAllUsersAction(
          session.user?.role === EUserRole.SCHOOL_MANAGER ? schoolId ?? undefined : undefined,
          EUserRole.TEACHER
        );
        console.log('Users response:', JSON.stringify(usersResponse, null, 2));

        // Fetch schools
        console.log('Fetching schools...');
        const schoolsResponse = await handleGetAllSchoolsAction();
        console.log('Schools response:', JSON.stringify(schoolsResponse, null, 2));

        // Process teachers data
        if (usersResponse.status === 'success') {
          // Add status and lastActivity for demo purposes
          // The API now filters by role, so client-side filtering is no longer needed here.
          const teachersWithStatus = (usersResponse.data || []).map(user => ({
            ...user,
            // Set status as 'active' if delete_at is null or undefined, otherwise use a random status
            status: !(user as any).delete_at ? 'active' : ['inactive', 'pending', 'suspended'][Math.floor(Math.random() * 3)],
            lastActivity: new Date(Date.now() - Math.floor(Math.random() * 30) * 24 * 60 * 60 * 1000).toISOString()
          }));

          setUsers(teachersWithStatus);
          console.log(`Found ${teachersWithStatus.length} teachers`);
        } else {
          const errorMessage = typeof usersResponse.message === 'string' ? usersResponse.message : 'Failed to fetch teachers';
          setError(errorMessage);
          console.error('Error fetching teachers:', usersResponse.message);
        }

        // Process schools data
        if (schoolsResponse.status === 'success') {
          const schoolsData = schoolsResponse.data || [];
          setSchools(schoolsData.map(school => ({ id: school.id, name: school.name })));
        } else {
          console.error('Error fetching schools:', schoolsResponse.message);
          // Don't set error for schools, as we still want to display teachers even if schools fetch fails
        }
      } catch (err) {
        console.error('Exception while fetching data:', err);
        setError(err instanceof Error ? err.message : 'An unexpected error occurred');
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [session]); // Add session as a dependency

  // Create the header component
  const headerTable = (
    <ListingHeader 
      title="Teacher Management" 
      subtitle="Manage all teachers in the system"
      buttonProps={{
        label: "Create Teacher",
        href: "/teacher-management/create",
        variant: "primary",
      }}
    />
  );

  // Create the error display component
  const errorDisplay = error ? <ErrorDisplay error={error} /> : null;

  // Create the users table component
  const isSchoolManager = session?.user?.role === EUserRole.SCHOOL_MANAGER;

  const table = (
    <UsersTable 
      users={users} 
      error={error || ''} 
      isLoading={isLoading}
      tableTitle="All Teachers"
      entityName="teacher"
      entityNamePlural="teachers"
      accountType="Teacher account"
      editPath="/teacher-management/edit/"
      createPath="/teacher-management/create"
      hideRoleColumn={true}
      hideSchoolColumn={isSchoolManager} // Conditionally hide school column
      hideRoleFilter={true} // Always hide role filter for teacher management page
      schools={schools}
    />
  );

  return (
    <div className="flex flex-col w-full h-full"> 
      {errorDisplay}
      <ListingTemplate 
        header={headerTable}
        table={table}
      />
    </div>
  );
};

export default TeacherManagementPage;
