'use client';

import React, { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation';
import { useForm, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { requiredString } from '@/utils/zod';
import { EUserRole } from '@/config/enums/user';
import { handleGetUserByIdAction, handleUpdateUserAction } from '@/actions/user.action';
import { handleGetAllSchoolsAction, fetchSchools } from '@/actions/school.action';
import {
  ArrowLeft,
  Loader2,
  School,
  User,
  Mail,
  Lock,
  ShieldCheck,
  AlertCircle,
  CheckCircle,
  Edit,
} from 'lucide-react';
import Link from 'next/link';
import { SchoolSelector } from '@/components/molecules/FormItems/SchoolSelector';

// Define the form schema using zod
const updateTeacherSchema = z.object({
  name: requiredString,
  email: requiredString.email('Please enter a valid email address'),
  // Make password optional for updates
  password: z.string().optional(),
  confirmPassword: z.string().optional(),
  role: z.nativeEnum(EUserRole),
  schoolId: z.string().optional(),
});

// Add validation for password confirmation only if password is provided
const updateTeacherFormSchema = updateTeacherSchema.refine(
  (data) => !data.password || data.password === data.confirmPassword,
  {
    message: 'Passwords do not match',
    path: ['confirmPassword'],
  }
);

type UpdateTeacherFormValues = z.infer<typeof updateTeacherFormSchema>;

const EditTeacherPage = () => {
  // Extract teacher ID from the URL parameters
  const params = useParams();
  const teacherId = params.id as string;

  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [schools, setSchools] = useState<{ id: string; name: string }[]>([]);
  const [isLoadingSchools, setIsLoadingSchools] = useState(false);
  const [formError, setFormError] = useState<string | null>(null);
  const [formSuccess, setFormSuccess] = useState<string | null>(null);
  const [teacher, setTeacher] = useState<any>(null);

  // Initialize the form
  const {
    register,
    handleSubmit,
    control,
    watch,
    formState: { errors, isDirty },
    setValue,
    reset,
  } = useForm<UpdateTeacherFormValues>({
    resolver: zodResolver(updateTeacherFormSchema),
    defaultValues: {
      name: '',
      email: '',
      password: '',
      confirmPassword: '',
      role: EUserRole.TEACHER,
      schoolId: '',
    },
  });

  // Watch the role field to conditionally render fields
  const selectedRole = watch('role');

  // Fetch teacher data when the component mounts
  useEffect(() => {
    const fetchTeacher = async () => {
      setIsLoading(true);
      try {
        const response = await handleGetUserByIdAction(teacherId);
        if (response.status === 'success' && response.data) {
          setTeacher(response.data);
          // Pre-populate the form with teacher data
          reset({
            name: response.data.name,
            email: response.data.email,
            password: '',
            confirmPassword: '',
            role: response.data.role,
            schoolId: response.data.schoolId || '',
          });
        } else {
          setFormError('Failed to fetch teacher details');
        }
      } catch (error) {
        console.error('Error fetching teacher:', error);
        setFormError('An unexpected error occurred while fetching teacher details');
      } finally {
        setIsLoading(false);
      }
    };

    fetchTeacher();
  }, [teacherId, reset]);

  // Fetch schools when the component mounts or when the role changes to SCHOOL_MANAGER or TEACHER
  useEffect(() => {
    const fetchSchools = async () => {
      if (selectedRole === EUserRole.SCHOOL_MANAGER || selectedRole === EUserRole.TEACHER) {
        setIsLoadingSchools(true);
        try {
          const response = await handleGetAllSchoolsAction();
          if (response.status === 'success' && response.data) {
            setSchools(response.data.map(school => ({ id: school.id, name: school.name })));
          } else {
            if (response.status !== 'success') {
              console.error('Failed to fetch schools:', response.message);
            }
          }
        } catch (error) {
          console.error('Error fetching schools:', error);
        } finally {
          setIsLoadingSchools(false);
        }
      }
    };

    fetchSchools();
  }, [selectedRole]);

  // Handle form submission
  const onSubmit = async (data: UpdateTeacherFormValues) => {
    setIsSubmitting(true);
    setFormError(null);
    setFormSuccess(null);

    try {
      // Remove confirmPassword from the payload
      const { confirmPassword, ...payload } = data;

      // Remove password if it's empty
      if (!payload.password) {
        delete payload.password;
      }

      // Ensure role is always teacher
      payload.role = EUserRole.TEACHER;

      const response = await handleUpdateUserAction(teacherId, payload);

      if (response.status === 'success') {
        setFormSuccess('Teacher updated successfully!');
        // Redirect to the teacher management page after a short delay
        setTimeout(() => {
          router.push(`/teacher-management`);
        }, 2000);
      } else {
        setFormError(Array.isArray(response.message) ? response.message.join(', ') : response.message);
      }
    } catch (error: any) {
      setFormError(error.message || 'An unexpected error occurred');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Get role badge styling
  const getRoleBadgeClass = (role: EUserRole) => {
    switch (role) {
      case EUserRole.ADMIN:
        return 'bg-purple-100 text-purple-800 border-purple-200';
      case EUserRole.TEACHER:
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case EUserRole.SCHOOL_MANAGER:
        return 'bg-green-100 text-green-800 border-green-200';
      case EUserRole.STUDENT:
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  if (isLoading) {
    return (
      <div className="container mx-auto p-4 flex justify-center items-center min-h-[50vh]">
        <div className="flex flex-col items-center">
          <div className="w-16 h-16 rounded-full bg-blue-100 flex items-center justify-center mb-4">
            <Loader2 size={32} className="animate-spin text-blue-600" />
          </div>
          <p className="text-lg font-medium text-gray-700">Loading teacher details...</p>
          <p className="text-sm text-gray-500 mt-2">Please wait while we fetch the teacher information</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-4 max-w-5xl">
      {/* Back button */}
      <div className="mb-6">
        <Link 
          href="/teacher-management" 
          className="inline-flex items-center gap-2 text-gray-900 hover:text-black transition-all duration-300 font-medium group"
        >
          <ArrowLeft size={18} className="group-hover:-translate-x-1 transition-transform duration-300" />
          Back to Teachers
        </Link>
      </div>

      {/* Page header */}
      <div className="overflow-hidden rounded-[0.5rem] border border-gray-200 shadow-sm bg-white mb-8">
        <div className="bg-dark-gray-300 border-b border-gray-200 p-5 flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <h3 className="text-xl font-bold text-gray-800 flex items-center gap-2">
              <div className="p-2 bg-gray-200 rounded-lg text-gray-600">
                <Edit size={20} />
              </div>
              Edit Teacher
            </h3>
            <p className="text-sm text-gray-600 mt-1.5">Update information for {teacher?.name}</p>
          </div>
          {teacher && (
            <div className={`inline-flex items-center px-4 py-2 rounded-full text-sm font-medium border shadow-sm ${getRoleBadgeClass(teacher.role)}`}>
              <ShieldCheck size={18} className="mr-2" />
              {teacher.role}
            </div>
          )}
        </div>
      </div>

      {/* Error and success messages */}
      {formError && (
        <div className="bg-red-50 border border-red-200 p-4 mb-6 rounded-lg shadow-sm">
          <div className="flex items-center">
            <div className="flex-shrink-0 text-red-500">
              <AlertCircle size={24} />
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">Error</h3>
              <p className="text-sm text-red-700 mt-1">{formError}</p>
            </div>
          </div>
        </div>
      )}

      {formSuccess && (
        <div className="bg-green-50 border border-green-200 p-4 mb-6 rounded-lg shadow-sm">
          <div className="flex items-center">
            <div className="flex-shrink-0 text-green-500">
              <CheckCircle size={24} />
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-green-800">Success</h3>
              <p className="text-sm text-green-700 mt-1">{formSuccess}</p>
            </div>
          </div>
        </div>
      )}

      {/* Form */}
      <form onSubmit={handleSubmit(onSubmit)} className="overflow-hidden rounded-[0.5rem] border border-gray-200 shadow-sm bg-white">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 p-8">
          {/* Teacher Information */}
          <div className="space-y-6">
            <div className="pb-3 mb-4 border-b border-gray-100">
              <h2 className="text-lg font-semibold text-gray-800 flex items-center gap-2">
                <div className="p-2 bg-gray-200 rounded-lg text-gray-600">
                  <User size={20} />
                </div>
                Teacher Information
              </h2>
              <p className="text-sm text-gray-600 mt-1.5">Basic information about the teacher</p>
            </div>

            <div className="form-control">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Full Name
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <User size={18} className="text-blue-500" />
                </div>
                <input
                  type="text"
                  className={`w-full pl-10 pr-3 py-2.5 border ${errors.name ? 'border-red-300 focus:ring-red-500 focus:border-red-500' : 'border-gray-300 focus:ring-blue-500 focus:border-blue-500'} rounded-lg shadow-sm focus:outline-none focus:ring-2 transition-all duration-300 hover:border-blue-300`}
                  {...register('name')}
                />
              </div>
              {errors.name && <p className="mt-2 text-sm text-red-600 flex items-center gap-1"><AlertCircle size={14} /> {errors.name.message}</p>}
            </div>

            <div className="form-control">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Email Address
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Mail size={18} className="text-blue-500" />
                </div>
                <input
                  type="email"
                  className={`w-full pl-10 pr-3 py-2.5 border ${errors.email ? 'border-red-300 focus:ring-red-500 focus:border-red-500' : 'border-gray-300 focus:ring-blue-500 focus:border-blue-500'} rounded-lg shadow-sm focus:outline-none focus:ring-2 transition-all duration-300 hover:border-blue-300`}
                  {...register('email')}
                />
              </div>
              {errors.email && <p className="mt-2 text-sm text-red-600 flex items-center gap-1"><AlertCircle size={14} /> {errors.email.message}</p>}
            </div>

            {/* Role is fixed as Teacher */}
            <input type="hidden" {...register('role')} value={EUserRole.TEACHER} />
          </div>

          {/* Password and School Information */}
          <div className="space-y-6">
            <div className="pb-3 mb-4 border-b border-gray-100">
              <h2 className="text-lg font-semibold text-gray-800 flex items-center gap-2">
                <div className="p-2 bg-gray-200 rounded-lg text-gray-600">
                  <Lock size={20} />
                </div>
                Security & Assignment
              </h2>
              <p className="text-sm text-gray-600 mt-1.5">Password and school assignment</p>
            </div>

            <div className="form-control">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Password <span className="text-gray-500 font-normal">(leave blank to keep current)</span>
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Lock size={18} className="text-gray-500" />
                </div>
                <input
                  type="password"
                  placeholder="••••••••"
                  className={`w-full pl-10 pr-3 py-2.5 border ${errors.password ? 'border-red-300 focus:ring-red-500 focus:border-red-500' : 'border-gray-300 focus:ring-gray-500 focus:border-gray-500'} rounded-lg shadow-sm focus:outline-none focus:ring-2 transition-all duration-300 hover:border-gray-300`}
                  {...register('password')}
                />
              </div>
              {errors.password && <p className="mt-2 text-sm text-red-600 flex items-center gap-1"><AlertCircle size={14} /> {errors.password.message}</p>}
            </div>

            <div className="form-control">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Confirm Password
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Lock size={18} className="text-gray-500" />
                </div>
                <input
                  type="password"
                  placeholder="••••••••"
                  className={`w-full pl-10 pr-3 py-2.5 border ${errors.confirmPassword ? 'border-red-300 focus:ring-red-500 focus:border-red-500' : 'border-gray-300 focus:ring-gray-500 focus:border-gray-500'} rounded-lg shadow-sm focus:outline-none focus:ring-2 transition-all duration-300 hover:border-gray-300`}
                  {...register('confirmPassword')}
                />
              </div>
              {errors.confirmPassword && <p className="mt-2 text-sm text-red-600 flex items-center gap-1"><AlertCircle size={14} /> {errors.confirmPassword.message}</p>}
            </div>

            {/* School Selection */}
            <div className="form-control">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Select School
              </label>
              <SchoolSelector
                control={control}
                name="schoolId"
                error={errors.schoolId}
                fetchSchools={fetchSchools}
                showCreateButton={false}
              />
            </div>
          </div>
        </div>

        {/* Form Actions */}
        <div className="px-5 py-4 bg-dark-gray-300 border-t border-gray-200 flex justify-end gap-4">
          <Link 
            href="/teacher-management" 
            className="px-5 py-2.5 bg-white text-gray-700 rounded-lg border border-gray-300 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-all duration-300 shadow-sm hover:shadow-md font-medium"
          >
            Cancel
          </Link>
          <button
            type="submit"
            className={`px-5 py-2.5 rounded-lg focus:outline-none focus:ring-2 focus:ring-offset-2 transition-all duration-300 shadow-sm hover:shadow-md font-medium flex items-center justify-center gap-2 min-w-[140px] ${
              isSubmitting || !isDirty 
                ? 'bg-gray-400 text-white cursor-not-allowed' 
                : 'bg-gray-700 text-white hover:bg-gray-800 focus:ring-gray-500'
            }`}
            disabled={isSubmitting || !isDirty}
          >
            {isSubmitting ? (
              <>
                <Loader2 size={18} className="animate-spin" />
                Updating...
              </>
            ) : (
              <>
                <CheckCircle size={18} />
                Update Teacher
              </>
            )}
          </button>
        </div>
      </form>
    </div>
  );
};

export default EditTeacherPage;
