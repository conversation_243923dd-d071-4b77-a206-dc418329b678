// Example: How to use the new "refresh" icon variant

import React, { useState } from 'react';
import { Button } from '@/components/atoms/Button/Button';
import Icon from '@/components/atoms/Icon/Icon';

export const RefreshIconExamples: React.FC = () => {
  const [isLoading, setIsLoading] = useState(false);

  const handleRefresh = () => {
    setIsLoading(true);
    // Simulate API call
    setTimeout(() => setIsLoading(false), 2000);
  };

  return (
    <div className="space-y-4 p-6">
      <h2 className="text-xl font-bold">Refresh Icon Examples</h2>
      
      {/* Example 1: Button with refresh icon */}
      <div>
        <h3 className="text-lg font-semibold mb-2">Button with Refresh Icon</h3>
        <Button
          variant="outline"
          iconProps={{ 
            variant: "refresh",
            className: isLoading ? 'animate-spin' : ''
          }}
          onClick={handleRefresh}
          disabled={isLoading}
        >
          {isLoading ? 'Refreshing...' : 'Refresh'}
        </Button>
      </div>

      {/* Example 2: Standalone refresh icon */}
      <div>
        <h3 className="text-lg font-semibold mb-2">Standalone Refresh Icon</h3>
        <Icon 
          variant="refresh" 
          size={6} 
          className={`cursor-pointer hover:text-blue-600 ${isLoading ? 'animate-spin' : ''}`}
          onClick={handleRefresh}
        />
      </div>

      {/* Example 3: Different sizes */}
      <div>
        <h3 className="text-lg font-semibold mb-2">Different Sizes</h3>
        <div className="flex items-center space-x-4">
          <Icon variant="refresh" size={4} />
          <Icon variant="refresh" size={6} />
          <Icon variant="refresh" size={8} />
          <Icon variant="refresh" size={10} />
        </div>
      </div>

      {/* Example 4: Comparison with refresh-ccw */}
      <div>
        <h3 className="text-lg font-semibold mb-2">Refresh vs Refresh-CCW</h3>
        <div className="flex items-center space-x-4">
          <div className="text-center">
            <Icon variant="refresh" size={6} />
            <p className="text-sm mt-1">refresh</p>
          </div>
          <div className="text-center">
            <Icon variant="refresh-ccw" size={6} />
            <p className="text-sm mt-1">refresh-ccw</p>
          </div>
        </div>
      </div>
    </div>
  );
};
