'use client';

import React, { useState, useEffect } from 'react';
import { AlertMessage } from '@/components/molecules/AlertMessage/AlertMessage';
import { NarrativeStructureViewer } from './NarrativeStructureViewer';
import { DeleteNarrativeStructureModal } from './DeleteNarrativeStructureModal';
import { handleDeleteNarrativeStructureAction } from '@/actions/narrative-structure.action';

interface NarrativeStructureManagerProps {
  schoolId: string;
  schoolName: string;
}

export const NarrativeStructureManager: React.FC<NarrativeStructureManagerProps> = ({
  schoolId,
  schoolName,
}) => {
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  // Clear messages after 5 seconds
  useEffect(() => {
    if (errorMessage || successMessage) {
      const timer = setTimeout(() => {
        setErrorMessage(null);
        setSuccessMessage(null);
      }, 5000);

      return () => clearTimeout(timer);
    }
  }, [errorMessage, successMessage]);

  const handleDelete = async () => {
    try {
      const response = await handleDeleteNarrativeStructureAction(schoolId);

      if (response.status === 'success') {
        setSuccessMessage('Narrative structure deleted successfully');
        setRefreshTrigger(prev => prev + 1);
      } else {
        setErrorMessage(response.message || 'Failed to delete narrative structure');
      }
    } catch (err: any) {
      setErrorMessage(err.message || 'An error occurred while deleting the narrative structure');
    } finally {
      setIsDeleteModalOpen(false);
    }
  };

  const handleExtractSuccess = () => {
    setSuccessMessage('Narrative structure extracted successfully');
  };

  const handleExtractError = (message: string) => {
    setErrorMessage(message);
  };

  return (
    <div className="space-y-4">
      {/* Alert Messages */}
      {errorMessage && (
        <AlertMessage 
          variant="error" 
          message={errorMessage} 
          className="mb-4 animate-fadeIn"
        />
      )}
      
      {successMessage && (
        <AlertMessage 
          variant="success" 
          message={successMessage} 
          className="mb-4 animate-fadeIn"
        />
      )}

      {/* Narrative Structure Viewer */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
        <h3 className="text-lg font-medium text-gray-800 mb-4">Narrative Structure</h3>
        
        <NarrativeStructureViewer
          schoolId={schoolId}
          schoolName={schoolName}
          onExtractSuccess={handleExtractSuccess}
          onExtractError={handleExtractError}
          onDeleteSuccess={() => setIsDeleteModalOpen(true)}
          key={`narrative-structure-${refreshTrigger}`}
        />
      </div>

      {/* Delete Modal */}
      <DeleteNarrativeStructureModal
        isOpen={isDeleteModalOpen}
        onClose={() => setIsDeleteModalOpen(false)}
        onDelete={handleDelete}
        schoolName={schoolName}
      />
    </div>
  );
};