'use client';

import React from 'react';
import { Calendar, User, CheckCircle, Clock, AlertTriangle } from 'lucide-react';
import { Button } from '@/components/atoms/Button/Button';
import { InfoField } from '@/components/molecules/InfoField/InfoField';

interface NarrativeStructureDetailsProps {
  schoolName?: string;
  status?: string;
  createdAt?: string;
  extractedBy?: string;
  onViewClick: () => void;
  onDeleteClick?: () => void;
  onExtractClick: () => void;
  isExtracting: boolean;
}

export const NarrativeStructureDetails: React.FC<NarrativeStructureDetailsProps> = ({
  schoolName,
  status,
  createdAt,
  extractedBy,
  onViewClick,
  onDeleteClick,
  onExtractClick,
  isExtracting,
}) => {
  const formatDate = (dateString?: string) => {
    if (!dateString) return 'Unknown';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const getStatusText = (status?: string) => {
    switch (status) {
      case 'COMPLETED':
        return (
          <div className="flex items-center gap-1.5">
            <CheckCircle size={14} className="text-green-500" />
            <span className="text-green-600 text-sm font-medium">Extraction completed</span>
          </div>
        );
      case 'PENDING':
        return (
          <div className="flex items-center gap-1.5">
            <Clock size={14} className="text-amber-500" />
            <span className="text-amber-600 text-sm font-medium">Extraction pending</span>
          </div>
        );
      case 'PROCESSING':
        return (
          <div className="flex items-center gap-1.5">
            <Clock size={14} className="text-amber-500" />
            <span className="text-amber-600 text-sm font-medium">Extraction in progress</span>
          </div>
        );
      case 'FAILED':
        return (
          <div className="flex items-center gap-1.5">
            <AlertTriangle size={14} className="text-red-500" />
            <span className="text-red-600 text-sm font-medium">Extraction failed</span>
          </div>
        );
      default:
        return null;
    }
  };

  return (
    <div className="w-full md:w-2/3 bg-white p-4 rounded-lg border border-gray-200 shadow-sm">
      <div className="flex flex-col sm:flex-row sm:items-center justify-between mb-3 gap-2">
        <div>
          <h4 className="font-medium text-gray-800 text-sm mb-1">
            {schoolName ? `${schoolName} Narrative Structure` : 'Narrative Structure'}
          </h4>
          {getStatusText(status)}
        </div>
        <div className="flex space-x-2">
          {status === 'COMPLETED' && (
            <Button
              variant="outline"
              onClick={onViewClick}
              iconProps={{
                variant: 'eye',
                className: 'w-4'
              }}
              className="!w-auto text-sm py-1 px-3 border-blue-200 text-blue-600 hover:bg-blue-50"
            >
              View
            </Button>
          )}
          <Button
            variant="error"
            onClick={onDeleteClick}
            iconProps={{
              variant: 'trash-2',
              className: 'w-4'
            }}
            className="!w-auto text-white text-sm py-1 px-3"
          >
            Delete
          </Button>
        </div>
      </div>
      
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 mb-3">
        {createdAt && (
          <InfoField
            label="Created"
            value={formatDate(createdAt)}
            icon={<Calendar className="text-blue-400 h-4 w-4" />}
            className="text-sm"
          />
        )}
        {extractedBy && (
          <InfoField
            label="Extracted By"
            value={extractedBy}
            icon={<User className="text-blue-400 h-4 w-4" />}
            className="text-sm"
          />
        )}
      </div>

      {(status === 'FAILED' || status === 'PENDING' || status === 'PROCESSING') && (
        <div className="mt-3">
          <Button
            variant="primary"
            onClick={onExtractClick}
            isLoading={isExtracting}
            iconProps={{
              variant: 'refresh-cw',
              className: 'w-4'
            }}
            className="!w-auto text-sm py-1 px-3"
          >
            {isExtracting ? 'Re-extracting...' : 'Re-extract Structure'}
          </Button>
        </div>
      )}
    </div>
  );
};