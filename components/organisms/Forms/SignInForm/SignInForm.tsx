'use client';
import { Button } from '@/components/atoms/Button/Button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/atoms/Form/Form';
import { Input } from '@/components/atoms/Input/Input';
import { CheckboxItem } from '@/components/molecules/CheckboxItem/CheckboxItem';
import { PasswordInput } from '@/components/molecules/PasswordInput/PasswordInput';
import { zodResolver } from '@hookform/resolvers/zod';
import { signIn } from 'next-auth/react';
import Link from 'next/link';
import React, { startTransition } from 'react';
import { useForm } from 'react-hook-form';
import { loginSchema, LoginValues } from './SignInForm.schema';

const SignInForm: React.FC = () => {
  const form = useForm<LoginValues>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      email: '',
      password: '',
      rememberMe: false,
    },
  });

  async function onSubmit(values: LoginValues) {
    console.log(1, values);
    startTransition(async () => {
      signIn('credentials', {
        email: values.email,
        password: values.password,
        redirect: true,
        callbackUrl: '/',
      });
    });
  }

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(onSubmit)}
        className="space-y-4 w-[30rem]"
      >
        <FormField
          control={form.control}
          name="email"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Email</FormLabel>
              <FormControl>
                <Input placeholder="Email" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="password"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Password</FormLabel>
              <FormControl>
                <PasswordInput placeholder="Password" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="flex items-center justify-between">
          <FormField
            control={form.control}
            name="rememberMe"
            render={({ field }) => (
              <FormItem>
                <FormControl>
                  <CheckboxItem
                    label={'Remember Me'}
                    {...field}
                    value={field.value ? 'true' : 'false'}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <div className="text-sm">
            <Link href="#" className="font-medium text-bright-blue">
              Forgot Password?
            </Link>
          </div>
        </div>

        <Button type="submit" className="w-full h-[3rem]">
          Sign In
        </Button>
      </form>
    </Form>
  );
};

export default SignInForm;
