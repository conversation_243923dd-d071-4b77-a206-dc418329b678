'use client';

import React, { useState, useEffect } from 'react'; // Added useEffect
import { useForm, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { requiredString } from '@/utils/zod';
import { EUserRole } from '@/config/enums/user';
import { handleCreateUserAction } from '@/actions/user.action';
import { ICreateUserPayload } from '@/apis/userApi'; // Corrected import path
import { useRouter } from 'next/navigation';
import {
  Loader2, Mail,
  School, ShieldCheck,
  User
} from 'lucide-react';
import { FormField } from '@/components/molecules/FormField/FormField';
import { AlertMessage } from '@/components/molecules/AlertMessage/AlertMessage';
import { SchoolSelector } from '@/components/molecules/FormItems/SchoolSelector';
import { fetchSchools } from '@/actions/school.action';

// Define the form schema using zod
const createUserSchema = z.object({
  name: requiredString,
  email: requiredString.email('Please enter a valid email address'),
  password: requiredString.min(8, 'Password must be at least 8 characters'),
  confirmPassword: requiredString,
  role: z.nativeEnum(EUserRole),
  schoolId: z.string().optional(),
});

// Add validation for password confirmation
const createUserFormSchema = createUserSchema.refine(
  (data) => data.password === data.confirmPassword,
  {
    message: 'Passwords do not match',
    path: ['confirmPassword'],
  }
);

export type CreateUserFormValues = z.infer<typeof createUserFormSchema>;

export interface UserFormProps {
  schools?: { id: string; name: string }[];
  isLoadingSchools?: boolean;
  onCreateSchool?: () => void;
  selectedSchoolId?: string;
  defaultRole?: EUserRole;
  redirectPath?: string;
  formTitle?: string;
  formSubtitle?: string;
  successMessage?: string;
  buttonText?: string;
  userType?: string;
  // Optional custom fetchSchools function
  customFetchSchools?: () => Promise<Array<{ id: string; name: string; address?: string }>>;
}

export const UserForm: React.FC<UserFormProps> = ({
  isLoadingSchools = false,
  onCreateSchool,
  selectedSchoolId,
  defaultRole = EUserRole.TEACHER,
  redirectPath = '/users-management',
  formTitle = 'User Information',
  formSubtitle = 'Basic information about the user',
  successMessage = 'User created successfully!',
  buttonText = 'Create User',
  userType = 'user',
  customFetchSchools,
}) => {
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formError, setFormError] = useState<string | null>(null);
  const [formSuccess, setFormSuccess] = useState<string | null>(null);

  // Initialize the form
  const {
    register,
    handleSubmit,
    control,
    watch,
    setValue, // Destructured setValue
    formState: { errors },
  } = useForm<CreateUserFormValues>({
    resolver: zodResolver(createUserFormSchema),
    defaultValues: {
      name: '',
      email: '',
      password: '',
      confirmPassword: '',
      role: defaultRole, // Use the provided defaultRole
      schoolId: '',
    },
  });

  // Watch the role field to conditionally render fields
  const selectedRole = watch('role');

  // Set schoolId when selectedSchoolId changes
  useEffect(() => {
    if (selectedSchoolId) {
      setValue('schoolId', selectedSchoolId);
    }
  }, [selectedSchoolId, setValue]);

  // Handle form submission
  const onSubmit = async (data: CreateUserFormValues) => {
    setIsSubmitting(true);
    setFormError(null);
    setFormSuccess(null);

    try {
      // Remove confirmPassword from the payload
      const { confirmPassword, ...restOfData } = data;
      let payload: ICreateUserPayload = { ...restOfData };

      if (userType === 'teacher') {
        payload = { ...payload, role: EUserRole.TEACHER };
      }

      const response = await handleCreateUserAction(payload);

      if (response.status === 'success') {
        setFormSuccess(successMessage);
        // Redirect to the specified path after a short delay
        setTimeout(() => {
          router.push(redirectPath);
        }, 2000);
      } else {
        setFormError(Array.isArray(response.message) ? response.message.join(', ') : response.message);
      }
    } catch (error: any) {
      setFormError(error.message || 'An unexpected error occurred');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div>
      <AlertMessage type="error" message={formError} />
      <AlertMessage type="success" message={formSuccess} />

      <form onSubmit={handleSubmit(onSubmit)} className="overflow-hidden rounded-[0.5rem] border border-gray-200 shadow-sm bg-white">
        <div className={`grid grid-cols-1 ${userType !== 'teacher' ? 'md:grid-cols-2' : ''} gap-8 p-8`}>
          {/* Common User Fields */}
          <div className="space-y-6">
            <div className="pb-3 mb-4 border-b border-gray-100">
              <h2 className="text-lg font-semibold text-gray-800 flex items-center gap-2">
                <div className="p-2 bg-gray-200 rounded-lg text-gray-600">
                  <User size={20} />
                </div>
                {formTitle}
              </h2>
              <p className="text-sm text-gray-600 mt-1.5">{formSubtitle}</p>
            </div>

            <FormField 
              label="Full Name" 
              error={errors.name?.message}
              icon={<User size={18} className="text-blue-500" />}
            >
              <input
                type="text"
                placeholder="Enter full name"
                className={`w-full pl-10 pr-3 py-2.5 border ${errors.name ? 'border-red-300 focus:ring-red-500 focus:border-red-500' : 'border-gray-300 focus:ring-blue-500 focus:border-blue-500'} rounded-lg shadow-sm focus:outline-none focus:ring-2 transition-all duration-300 hover:border-blue-300`}
                {...register('name')}
              />
            </FormField>

            <FormField 
              label="Email Address" 
              error={errors.email?.message}
              icon={<Mail size={18} className="text-blue-500" />}
            >
              <input
                type="email"
                placeholder="Enter email address"
                className={`w-full pl-10 pr-3 py-2.5 border ${errors.email ? 'border-red-300 focus:ring-red-500 focus:border-red-500' : 'border-gray-300 focus:ring-blue-500 focus:border-blue-500'} rounded-lg shadow-sm focus:outline-none focus:ring-2 transition-all duration-300 hover:border-blue-300`}
                {...register('email')}
              />
            </FormField>

            <FormField 
              label="Password" 
              error={errors.password?.message}
            >
              <input
                type="password"
                placeholder="Enter password"
                className={`w-full pl-10 pr-3 py-2.5 border ${errors.password ? 'border-red-300 focus:ring-red-500 focus:border-red-500' : 'border-gray-300 focus:ring-gray-500 focus:border-gray-500'} rounded-lg shadow-sm focus:outline-none focus:ring-2 transition-all duration-300 hover:border-gray-300`}
                {...register('password')}
              />
            </FormField>

            <FormField 
              label="Confirm Password" 
              error={errors.confirmPassword?.message}
            >
              <input
                type="password"
                placeholder="Confirm password"
                className={`w-full pl-10 pr-3 py-2.5 border ${errors.confirmPassword ? 'border-red-300 focus:ring-red-500 focus:border-red-500' : 'border-gray-300 focus:ring-gray-500 focus:border-gray-500'} rounded-lg shadow-sm focus:outline-none focus:ring-2 transition-all duration-300 hover:border-gray-300`}
                {...register('confirmPassword')}
              />
            </FormField>

            {userType !== 'teacher' && (
              <FormField
                label="Role"
                error={errors.role?.message}
                icon={<ShieldCheck size={18} className="text-blue-500" />}
              >
                <Controller
                  name="role"
                  control={control}
                  render={({ field }) => (
                    <select
                      className={`w-full pl-10 pr-3 py-2.5 border ${errors.role ? 'border-red-300 focus:ring-red-500 focus:border-red-500' : 'border-gray-300 focus:ring-blue-500 focus:border-blue-500'} rounded-lg shadow-sm focus:outline-none focus:ring-2 transition-all duration-300 hover:border-blue-300 bg-white appearance-none`}
                      {...field}
                    >
                      <option value={EUserRole.ADMIN}>Admin</option>
                      <option value={EUserRole.TEACHER}>Teacher</option>
                      <option value={EUserRole.SCHOOL_MANAGER}>School Manager</option>
                      <option value={EUserRole.STUDENT}>Student</option>
                    </select>
                  )}
                />
                <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                  <svg className="h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
                  </svg>
                </div>
              </FormField>
            )}
          </div>

          {/* Conditional UI based on role */}
          {userType !== 'teacher' && selectedRole !== EUserRole.ADMIN && (
          <div className="space-y-6">
            {(selectedRole === EUserRole.SCHOOL_MANAGER || selectedRole === EUserRole.TEACHER || selectedRole === EUserRole.STUDENT) ? (
              <div>
                <div className="pb-3 mb-4 border-b border-gray-100">
                  <h2 className="text-lg font-semibold text-gray-800 flex items-center gap-2">
                    <div className="p-2 bg-gray-200 rounded-lg text-gray-600">
                      <School size={20} />
                    </div>
                    School Information
                  </h2>
                  <p className="text-sm text-gray-600 mt-1.5">School assignment for this {userType}</p>
                </div>

                <SchoolSelector
                  name="schoolId"
                  label="Select School"
                  required={true}
                  showCreateButton={selectedRole === EUserRole.SCHOOL_MANAGER && !!onCreateSchool}
                  onCreateSchool={onCreateSchool}
                  disabled={isLoadingSchools || isSubmitting}
                  control={control}
                  error={errors.schoolId}
                  fetchSchools={customFetchSchools || fetchSchools}
                />
                {isLoadingSchools && <p className="mt-2 text-sm text-gray-500 flex items-center gap-1"><Loader2 size={14} className="animate-spin" /> Loading schools...</p>}
              </div>
            ) : (
              <div className="flex items-center justify-center h-full">
                <div className="text-center p-8 bg-gray-50 rounded-lg border border-gray-200 w-full shadow-sm hover:shadow-md transition-all duration-300">
                  <div className="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-blue-100 mb-5 shadow-sm">
                    <User size={28} className="text-blue-600" />
                  </div>
                  <h3 className="text-lg font-medium text-gray-900">Role-specific settings</h3>
                  <p className="mt-3 text-sm text-gray-600">
                    Additional settings will appear here based on the selected role.
                  </p>
                </div>
              </div>
            )}
          </div>
          )}
        </div>

        <div className="px-5 py-4 bg-dark-gray-300 border-t border-gray-200 flex justify-end gap-4">
          <button
            type="submit"
            className={`px-5 py-2.5 rounded-lg focus:outline-none focus:ring-2 focus:ring-offset-2 transition-all duration-300 shadow-sm hover:shadow-md font-medium flex items-center justify-center gap-2 min-w-[140px] ${
              isSubmitting 
                ? 'bg-gray-400 text-white cursor-not-allowed' 
                : 'bg-gray-700 text-white hover:bg-gray-800 focus:ring-gray-500'
            }`}
            disabled={isSubmitting}
          >
            {isSubmitting ? (
              <>
                <Loader2 size={18} className="animate-spin" />
                Creating...
              </>
            ) : (
              <>
                <User size={18} />
                {buttonText}
              </>
            )}
          </button>
        </div>
      </form>
    </div>
  );
};
