'use client';

import React from 'react';
import { AlertCircle, CheckCircle2 } from 'lucide-react';

export interface FormFieldProps {
  label: string;
  error?: string;
  children: React.ReactNode;
  icon?: React.ReactNode;
  required?: boolean;
  success?: boolean;
  hint?: string;
}

export const FormField: React.FC<FormFieldProps> = ({
  label,
  error,
  children,
  icon,
  required = false,
  success = false,
  hint,
}) => {
  return (
    <div className="form-control space-y-2">
      <label className="block text-sm font-medium text-gray-700">
        {label}
        {required && <span className="text-red-500 ml-1">*</span>}
      </label>
      
      <div className="relative">
        {icon && (
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none z-10">
            {icon}
          </div>
        )}
        {children}
        {success && !error && (
          <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
            <CheckCircle2 size={16} className="text-green-500" />
          </div>
        )}
      </div>
      
      {error && (
        <div className="flex items-start gap-2 mt-2">
          <AlertCircle size={14} className="text-red-500 mt-0.5 flex-shrink-0" />
          <p className="text-sm text-red-600 leading-tight">{error}</p>
        </div>
      )}
      
      {hint && !error && (
        <p className="text-xs text-gray-500 leading-tight">{hint}</p>
      )}
    </div>
  );
};
