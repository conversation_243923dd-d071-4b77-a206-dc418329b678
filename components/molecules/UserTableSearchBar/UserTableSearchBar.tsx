'use client';

import React from 'react';
import { Search, X } from 'lucide-react';

export interface UserTableSearchBarProps {
  searchTerm: string;
  onSearchChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onClearSearch: () => void;
  placeholder?: string;
}

export const UserTableSearchBar: React.FC<UserTableSearchBarProps> = ({
  searchTerm,
  onSearchChange,
  onClearSearch,
  placeholder = 'Search users...'
}) => {
  return (
    <div className="relative flex-grow sm:w-72">
      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
        <Search size={18} className="text-gray-500" />
      </div>
      <input
        type="text"
        placeholder={placeholder}
        value={searchTerm}
        onChange={onSearchChange}
        className="block w-full pl-10 pr-3 py-2.5 border border-gray-300 rounded-lg text-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 shadow-sm transition-all duration-200"
        aria-label="Search users"
      />
      {searchTerm && (
        <button 
          onClick={onClearSearch}
          className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600"
          aria-label="Clear search"
        >
          <X size={16} />
        </button>
      )}
    </div>
  );
};