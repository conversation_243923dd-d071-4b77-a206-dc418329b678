import { ElementType, forwardRef } from 'react';

import { cn } from '@/utils/cn';

interface ContainerProps extends React.ComponentProps<'div'> {
  asElement?: ElementType;
}

const Container = forwardRef((props: ContainerProps, ref) => {
  const { className, children, asElement: Component = 'div', ...rest } = props;

  return (
    <Component
      ref={ref}
      className={cn('container mx-auto', className)}
      {...rest}
    >
      {children}
    </Component>
  );
});

Container.displayName = 'Container';

export default Container;
