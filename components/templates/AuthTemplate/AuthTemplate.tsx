import { CustomImage } from '@/components/atoms/CustomImage/CustomImage';
import Icon from '@/components/atoms/Icon';
import React from 'react';

interface AuthTemplateProps {
  children: React.ReactNode;
}

const AuthTemplate: React.FC<AuthTemplateProps> = ({ children }) => {
  return (
    <div className="flex h-screen">
      {/* Left Side */}
      <div className="relative min-w-[50rem] flex flex-col items-center justify-center">
        <div className="absolute z-10 top-10 left-10">
          <Icon variant="logo" size={61.5} />
        </div>
        <div className="absolute top-0 w-full h-full">
          <CustomImage
            className="rounded-tr-[2rem] rounded-br-[2rem]"
            src={'/assets/sign-in.png'}
            alt={'sign-in.png'}
          />
        </div>
      </div>

      {/* Right Side */}
      <div className="w-1/2 flex flex-col justify-center px-20 gap-6">
        <div className="text-3xl text-black-100 font-bold text-large leading-[45px] tracking-[0%] align-middle mb-2">
          Welcome to EduSG!
          <br /> Please Sign in to continue.
        </div>
        <p className="text-dark-gray font-normal text-[14px] leading-[28px] align-middle  mb-6">
          By signing up, you will gain access to exclusive content, special
          offers, and be the first to hear about exciting news and updates.
        </p>
        {children}
      </div>
    </div>
  );
};

export default AuthTemplate;
