import React from 'react';

export interface DetailTemplateProps {
  header: React.ReactNode;
  content: React.ReactNode;
  error?: React.ReactNode;
  loading?: React.ReactNode;
}

export const DetailTemplate: React.FC<DetailTemplateProps> = ({
  header,
  content,
  error,
  loading,
}) => {
  return (
    <div className="flex flex-col w-full h-full gap-2">
      {header}
      {error}
      {loading}
      {content}
    </div>
  );
};